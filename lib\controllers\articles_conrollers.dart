import 'dart:convert';

import 'package:articles_backend_admin/models/articles.dart';
import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../repositories/i_backend_api.dart';

class ArticlesController extends ChangeNotifier {
  ArticlesController() {
    init();
  }

  Future<void> init() async {
    await fetchArticles();
  }

  Articles? _articles;
  Articles? get articles => _articles;

  Future<void> fetchArticles() async {
    final response = await di<IBackendApi>().getArticles();

    _articles = Articles.fromJson(json.decode(response.body));

    notifyListeners();
  }

  Future<void> approveArticle(int id) async {
    await di<IBackendApi>().approveArticle(id);
    await fetchArticles();
  }

  Future<void> deleteArticle(int id) async {
    await di<IBackendApi>().deleteArticle(id);
    await fetchArticles();
  }

  
}
