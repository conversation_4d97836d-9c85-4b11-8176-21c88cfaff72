import 'package:articles_backend_admin/globals.dart';
import 'package:flutter/material.dart';

import 'locator/injection_container.dart';
import 'router/router.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  initializeDependencies();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      builder: (context, child) {
        final MediaQueryData data = MediaQuery.of(context);
        return MediaQuery(
          data: data.copyWith(textScaler: TextScaler.noScaling),
          child: child!,
        );
      },
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      scaffoldMessengerKey: snackbarKey,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color.fromARGB(255, 101, 178, 255),
        ),
      ),
    );
  }
}
