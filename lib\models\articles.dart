class Articles {
  int? count;
  List<Data>? data;
  bool? success;

  Articles({this.count, this.data, this.success});

  Articles.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(Data.fromJson(v));
      });
    }
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['success'] = success;
    return data;
  }
}

class Data {
  int? id;
  String? createdAt;
  String? authorName;
  String? emailAddress;
  int? categoryId;
  String? articleTitle;
  String? articleBody;
  String? message;
  bool? acceptTc;
  bool? paid;
  bool? approved;
  String? url;
  String? keyword;

  Data(
      {this.id,
      this.createdAt,
      this.authorName,
      this.emailAddress,
      this.categoryId,
      this.articleTitle,
      this.articleBody,
      this.message,
      this.acceptTc,
      this.paid,
      this.approved,
      this.url,
      this.keyword});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createdAt = json['created_at'];
    authorName = json['author_name'];
    emailAddress = json['email_address'];
    categoryId = json['category_id'];
    articleTitle = json['article_title'];
    articleBody = json['article_body'];
    message = json['message'];
    acceptTc = json['accept_tc'];
    paid = json['paid'];
    approved = json['approved'];
    url = json['url'];
    keyword = json['keyword'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['created_at'] = createdAt;
    data['author_name'] = authorName;
    data['email_address'] = emailAddress;
    data['category_id'] = categoryId;
    data['article_title'] = articleTitle;
    data['article_body'] = articleBody;
    data['message'] = message;
    data['accept_tc'] = acceptTc;
    data['paid'] = paid;
    data['approved'] = approved;
    data['url'] = url;
    data['keyword'] = keyword;
    return data;
  }
}