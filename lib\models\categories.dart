class Categories {
  int? count;
  List<CategoryData>? data;
  bool? success;

  Categories({this.count, this.data, this.success});

  Categories.fromJson(Map<String, dynamic> json) {
    count = json['count'];
    if (json['data'] != null) {
      data = <CategoryData>[];
      json['data'].forEach((v) {
        data!.add(CategoryData.fromJson(v));
      });
    }
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['count'] = count;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['success'] = success;
    return data;
  }
}

class CategoryData {
  int? id;
  String? categoryName;
  String? link;

  CategoryData({this.id, this.categoryName, this.link});

  CategoryData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    categoryName = json['category_name'];
    link = json['link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['category_name'] = categoryName;
    data['link'] = link;
    return data;
  }

  @override
  String toString() {
    return 'CategoryData{id: $id, categoryName: $categoryName, link: $link}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CategoryData &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          categoryName == other.categoryName &&
          link == other.link;

  @override
  int get hashCode => id.hashCode ^ categoryName.hashCode ^ link.hashCode;
}
