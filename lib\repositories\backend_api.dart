import '../server_config/server_config.dart';
import 'i_backend_api.dart';
import 'package:http/http.dart' as http;

class BackendApi implements IBackendApi {
  @override
  Future<http.Response> getArticles() async {
    try {
      final response = await http.get(
        Uri.parse("${ServerConfig.serverUrl}/articles"),
        headers: {
          'Authorization': 'Bearer ${ServerConfig.apiKey}',
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        return response;
      } else {
        throw Exception('Failed to load articles');
      }
    } catch (e) {
      throw Exception('Failed to load articles: $e');
    }
  }

  @override
  Future<http.Response> approveArticle(int id) async {
    try {
      final response = await http.post(
        Uri.parse("${ServerConfig.serverUrl}/articles-approve/$id"),
        headers: {
          'Authorization': 'Bearer ${ServerConfig.apiKey}',
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        return response;
      } else {
        throw Exception('Failed to approve article');
      }
    } catch (e) {
      throw Exception('Failed to approve article: $e');
    }
  }

  @override
  Future<void> deleteArticle(int id) async {
    try {
      final response = await http.delete(
        Uri.parse("${ServerConfig.serverUrl}/articles-delete/$id"),
        headers: {
          'Authorization': 'Bearer ${ServerConfig.apiKey}',
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode != 200) {
        throw Exception('Failed to delete article');
      }
    } catch (e) {
      throw Exception('Failed to delete article: $e');
    }
  }

  @override
  Future<http.Response> getCategories() async {
    try {
      final response = await http.get(
        Uri.parse("${ServerConfig.serverUrl}/get-categories"),
        headers: {
          'Authorization': 'Bearer ${ServerConfig.apiKey}',
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode == 200) {
        return response;
      } else {
        throw Exception('Failed to load categories');
      }
    } catch (e) {
      throw Exception('Failed to load categories: $e');
    }
  }
}
