import 'package:go_router/go_router.dart';

import '../screens/articles_screen.dart';
import '../screens/main_screen.dart';

final GoRouter router = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: '/',
  routes: [
    GoRoute(path: '/', builder: (context, state) => const MainScreen()),
    GoRoute(
      path: '/articles',
      builder: (context, state) => const ArticlesScreen(),
    ),
  ],
);
