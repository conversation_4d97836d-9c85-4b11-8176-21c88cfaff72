import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/articles_conrollers.dart';

class ArticlesScreen extends StatelessWidget with WatchItMixin {
  const ArticlesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final articles = watchIt<ArticlesController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Articles')),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: articles.articles?.data?.length ?? 0,
        itemBuilder: (context, index) {
          final article = articles.articles?.data?[index];
          return Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          article?.articleTitle ?? '',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            article?.paid == true
                                ? Icons.paid
                                : Icons.money_off,
                            color: article?.paid == true
                                ? Colors.green
                                : Colors.grey,
                            size: 30,
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            article?.approved == true
                                ? Icons.check_circle
                                : Icons.pending,
                            color: article?.approved == true
                                ? Colors.green
                                : Colors.orange,
                            size: 30,
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'By ${article?.authorName ?? ''} • ${article?.emailAddress ?? ''}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 18),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    article?.articleBody ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 8),
                  if (article?.message?.isNotEmpty == true) ...[
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Message: ${article?.message}',
                        style: TextStyle(color: Colors.blue[800], fontSize: 18),
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Category: ${article?.categoryId ?? ''}',
                        style: const TextStyle(fontSize: 16),
                      ),
                      Text(
                        'Keyword: ${article?.keyword ?? ''}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                  if (article?.url?.isNotEmpty == true) ...[
                    const SizedBox(height: 4),
                    Text(
                      'URL: ${article?.url}',
                      style: TextStyle(color: Colors.blue[600], fontSize: 20),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    'Created: ${article?.createdAt ?? ''}',
                    style: TextStyle(color: Colors.grey[500], fontSize: 18),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: article?.id != null
                              ? () => _showApproveDialog(
                                  context,
                                  articles,
                                  article!.id!,
                                )
                              : null,
                          icon: const Icon(
                            Icons.check_circle,
                            color: Colors.white,
                          ),
                          label: const Text(
                            'Approve',
                            style: TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: article?.id != null
                              ? () => _showDeleteDialog(
                                  context,
                                  articles,
                                  article!.id!,
                                )
                              : null,
                          icon: const Icon(Icons.delete, color: Colors.white),
                          label: const Text(
                            'Delete',
                            style: TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showApproveDialog(
    BuildContext context,
    ArticlesController articles,
    int articleId,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Approve Article'),
          content: const Text('Are you sure you want to approve this article?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                await di<ArticlesController>().approveArticle(articleId);
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: const Text(
                'Approve',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    ArticlesController articles,
    int articleId,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Article'),
          content: const Text(
            'Are you sure you want to delete this article? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                await di<ArticlesController>().deleteArticle(articleId);
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text(
                'Delete',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
