import 'package:flutter/material.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/articles_conrollers.dart';

class CategoriesScreen extends StatelessWidget with WatchItMixin {
  const CategoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final categories = watchIt<ArticlesController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Categories')),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: categories.categories?.data?.length ?? 0,
        itemBuilder: (context, index) {
          final category = categories.categories?.data?[index];
          return Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category?.categoryName ?? '',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    category?.link ?? '',
                    style: const TextStyle(fontSize: 18),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
