import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:watch_it/watch_it.dart';

import '../controllers/articles_conrollers.dart';

class MainScreen extends StatelessWidget with WatchItMixin {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final articles = watchIt<ArticlesController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Main Screen')),
      body: Center(
        child: ElevatedButton(
          onPressed: () => context.push('/articles'),
          child: const Text('Articles'),
        ),
      ),
    );
  }
}
