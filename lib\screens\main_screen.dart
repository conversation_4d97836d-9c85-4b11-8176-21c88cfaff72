import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Admin')),
      body: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              style: ElevatedButton.styleFrom(minimumSize: const Size(150, 50)),
              onPressed: () => context.push('/articles'),
              child: const Text('Articles'),
            ),
            const SizedBox(width: 20),
            ElevatedButton(
              style: ElevatedButton.styleFrom(minimumSize: const Size(150, 50)),
              onPressed: () => context.push('/categories'),
              child: const Text('Categories'),
            ),
          ],
        ),
      ),
    );
  }
}
